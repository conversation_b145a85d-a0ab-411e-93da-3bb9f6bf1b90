import { Heart, Shield, Award, Users, Globe, Target, Lightbulb, CheckCircle } from 'lucide-react'
import SEOHead from '../components/SEOHead'
import { seoData } from '../data/seoData'

const About = () => {
  const values = [
    {
      icon: Heart,
      title: "Patient-Centered Innovation",
      description: "Every product we develop is designed with patient outcomes as our primary focus, ensuring life-saving technology reaches those who need it most."
    },
    {
      icon: Shield,
      title: "Uncompromising Quality",
      description: "We maintain the highest standards in manufacturing and testing, with rigorous quality control processes that exceed industry requirements."
    },
    {
      icon: Award,
      title: "Clinical Excellence",
      description: "Our products are developed in collaboration with leading medical professionals to ensure they meet real-world clinical needs."
    },
    {
      icon: Globe,
      title: "Global Impact",
      description: "We're committed to making advanced ventilation technology accessible to healthcare facilities worldwide, regardless of location or size."
    }
  ]

  const milestones = [
    {
      year: "2009",
      title: "Company Founded",
      description: "Foremost Meditech was established with a mission to revolutionize ventilation technology."
    },
    {
      year: "2012",
      title: "First FDA Approval",
      description: "Received FDA 510(k) clearance for our flagship ICU ventilator system."
    },
    {
      year: "2015",
      title: "International Expansion",
      description: "Expanded operations to serve healthcare facilities across 25 countries."
    },
    {
      year: "2018",
      title: "ISO Certification",
      description: "Achieved ISO 13485 certification for medical device quality management."
    },
    {
      year: "2020",
      title: "Emergency Response",
      description: "Rapidly scaled production to support global COVID-19 response efforts."
    },
    {
      year: "2024",
      title: "50+ Countries",
      description: "Now serving healthcare facilities in over 50 countries worldwide."
    }
  ]

  const team = [
    {
      name: "Dr. Sarah Chen",
      role: "Chief Executive Officer",
      image: "/api/placeholder/300/300",
      bio: "Former ICU physician with 15+ years of clinical experience and expertise in medical device development."
    },
    {
      name: "Michael Rodriguez",
      role: "Chief Technology Officer",
      image: "/api/placeholder/300/300",
      bio: "Biomedical engineer with 20+ years in respiratory care technology and product innovation."
    },
    {
      name: "Dr. Emily Watson",
      role: "Chief Medical Officer",
      image: "/api/placeholder/300/300",
      bio: "Pulmonologist and researcher specializing in mechanical ventilation and critical care medicine."
    },
    {
      name: "David Kim",
      role: "VP of Quality Assurance",
      image: "/api/placeholder/300/300",
      bio: "Quality systems expert with extensive experience in FDA regulations and medical device compliance."
    }
  ]

  const certifications = [
    { name: "FDA 510(k)", description: "US Food and Drug Administration clearance" },
    { name: "CE Mark", description: "European Conformity certification" },
    { name: "ISO 13485", description: "Medical device quality management" },
    { name: "ISO 14971", description: "Medical device risk management" },
    { name: "IEC 60601", description: "Medical electrical equipment safety" },
    { name: "Health Canada", description: "Canadian medical device license" }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead {...seoData.about} />
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-medical-600 via-medical-700 to-medical-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              About Foremost Meditech
            </h1>
            <p className="text-xl lg:text-2xl text-medical-100 max-w-4xl mx-auto leading-relaxed">
              For over 15 years, we've been at the forefront of ventilation technology, 
              developing life-saving medical devices that healthcare professionals trust worldwide.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="space-y-8">
                <div>
                  <div className="flex items-center space-x-3 mb-4">
                    <Target className="w-8 h-8 text-medical-600" />
                    <h2 className="text-3xl font-bold text-gray-900">Our Mission</h2>
                  </div>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    To save lives by developing and manufacturing the world's most advanced, 
                    reliable, and accessible ventilation systems. We believe that every patient 
                    deserves access to life-saving respiratory care, regardless of where they are.
                  </p>
                </div>
                <div>
                  <div className="flex items-center space-x-3 mb-4">
                    <Lightbulb className="w-8 h-8 text-medical-600" />
                    <h2 className="text-3xl font-bold text-gray-900">Our Vision</h2>
                  </div>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    To be the global leader in ventilation technology, setting new standards 
                    for innovation, quality, and patient outcomes. We envision a world where 
                    advanced respiratory care is available to every healthcare facility.
                  </p>
                </div>
              </div>
            </div>
            <div className="relative">
              <img
                src="/api/placeholder/600/400"
                alt="Foremost Meditech manufacturing facility in Sirsa, Haryana - Advanced medical equipment production"
                className="w-full h-auto rounded-2xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These principles guide everything we do, from product development 
              to customer service and global partnerships.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center justify-center w-16 h-16 bg-medical-600 rounded-lg mb-6">
                  <value.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From a small startup to a global leader in ventilation technology, 
              here are the key milestones that have shaped our company.
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-medical-200"></div>
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-medical-600">
                      <div className="text-2xl font-bold text-medical-600 mb-2">
                        {milestone.year}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {milestone.title}
                      </h3>
                      <p className="text-gray-600">
                        {milestone.description}
                      </p>
                    </div>
                  </div>
                  <div className="relative flex items-center justify-center w-8 h-8 bg-medical-600 rounded-full border-4 border-white shadow-lg z-10">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                  </div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Leadership Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our experienced leadership team combines deep medical expertise 
              with proven business acumen to drive innovation and growth.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <img
                  src={member.image}
                  alt={`${member.name} - ${member.role} at Foremost Meditech`}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {member.name}
                  </h3>
                  <div className="text-medical-600 font-medium mb-3">
                    {member.role}
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {member.bio}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Certifications & Compliance
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We maintain the highest standards of quality and safety through 
              rigorous certification processes and regulatory compliance.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {certifications.map((cert, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6 text-center hover:bg-gray-100 transition-colors duration-200">
                <div className="flex items-center justify-center w-16 h-16 bg-medical-600 rounded-full mx-auto mb-4">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {cert.name}
                </h3>
                <p className="text-gray-600 text-sm">
                  {cert.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-medical-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Join Our Mission
          </h2>
          <p className="text-xl text-medical-100 mb-8 max-w-3xl mx-auto">
            Whether you're a healthcare professional, distributor, or potential partner, 
            we'd love to hear from you and explore how we can work together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-medical-700 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Contact Us
            </a>
            <a
              href="#careers"
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-medical-700 transition-colors duration-200"
            >
              View Careers
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About
