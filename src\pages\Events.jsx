import { Calendar, Award, Users, Globe, TrendingUp, MapPin, Clock, ExternalLink, Star, Trophy, Target } from 'lucide-react'
import SEOHead from '../components/SEOHead'
import { seoData } from '../data/seoData'

const Events = () => {
  const achievements = [
    {
      icon: Award,
      title: "ISO 13485 Certification",
      description: "Certified for quality management systems in medical device manufacturing",
      year: "2023",
      category: "Quality Certification"
    },
    {
      icon: Trophy,
      title: "Healthcare Innovation Award",
      description: "Recognized for breakthrough ventilation technology at Medical Expo India",
      year: "2023",
      category: "Industry Recognition"
    },
    {
      icon: Star,
      title: "CE Mark Certification",
      description: "European conformity certification for all our medical devices",
      year: "2022",
      category: "International Certification"
    },
    {
      icon: Target,
      title: "FDA 510(k) Clearance",
      description: "US FDA clearance for our advanced ventilator systems",
      year: "2022",
      category: "Regulatory Approval"
    }
  ]

  const milestones = [
    {
      number: "10+",
      label: "Years in Business",
      description: "Over a decade of excellence in medical technology"
    },
    {
      number: "10,000+",
      label: "Lives Saved",
      description: "Patients supported by our ventilation systems"
    },
    {
      number: "200+",
      label: "Hospitals Served",
      description: "Healthcare facilities across India using our equipment"
    },
    {
      number: "25+",
      label: "States Covered",
      description: "Pan-India presence with comprehensive support"
    }
  ]

  const upcomingEvents = [
    {
      title: "Medical Technology Expo 2024",
      date: "March 15-17, 2024",
      location: "Mumbai, India",
      type: "Trade Show",
      description: "Join us at India's largest medical technology exhibition where we'll showcase our latest ventilation innovations.",
      status: "upcoming"
    },
    {
      title: "Critical Care Conference",
      date: "April 22-24, 2024", 
      location: "Delhi, India",
      type: "Conference",
      description: "Presenting our research on advanced ventilation protocols in critical care settings.",
      status: "upcoming"
    },
    {
      title: "Healthcare Innovation Summit",
      date: "May 10-12, 2024",
      location: "Bangalore, India",
      type: "Summit",
      description: "Participating in discussions about the future of medical technology and patient care.",
      status: "upcoming"
    }
  ]

  const pastEvents = [
    {
      title: "ACRM Annual Conference 2023",
      date: "November 8-10, 2023",
      location: "Chennai, India",
      type: "Conference",
      description: "Successfully presented our latest research on ventilator-associated pneumonia prevention.",
      highlights: ["Best Paper Award", "200+ Attendees", "Live Product Demo"]
    },
    {
      title: "Medical Device Expo 2023",
      date: "September 20-22, 2023",
      location: "Hyderabad, India", 
      type: "Exhibition",
      description: "Showcased our FMT 2100 ventilator series to healthcare professionals nationwide.",
      highlights: ["50+ New Partnerships", "Product Launch", "Media Coverage"]
    },
    {
      title: "International Respiratory Care Symposium",
      date: "June 15-17, 2023",
      location: "Mumbai, India",
      type: "Symposium",
      description: "Conducted workshops on advanced ventilation techniques for respiratory therapists.",
      highlights: ["300+ Participants", "Hands-on Training", "Certification Program"]
    }
  ]

  return (
    <div className="min-h-screen">
      <SEOHead {...seoData.events} />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-medical-600 via-medical-700 to-medical-800 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold leading-tight mb-6">
              Events & 
              <span className="block text-medical-200">Achievements</span>
            </h1>
            <p className="text-xl lg:text-2xl text-medical-100 leading-relaxed max-w-3xl mx-auto mb-8">
              Celebrating our journey of innovation, industry recognition, and commitment to advancing 
              healthcare technology through meaningful partnerships and achievements.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#upcoming"
                className="inline-flex items-center justify-center px-8 py-4 bg-white text-medical-700 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                Upcoming Events
              </a>
              <a
                href="#achievements"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-medical-700 transition-colors duration-200"
              >
                Our Achievements
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Company Milestones Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Journey in Numbers
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These milestones represent our commitment to excellence and the trust 
              healthcare professionals place in our medical technology solutions.
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="text-center bg-white rounded-xl p-8 shadow-lg">
                <div className="text-4xl lg:text-5xl font-bold text-medical-600 mb-2">
                  {milestone.number}
                </div>
                <div className="text-lg font-semibold text-gray-900 mb-2">
                  {milestone.label}
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {milestone.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section id="achievements" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Awards & Recognition
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our dedication to quality and innovation has been recognized by industry leaders, 
              regulatory bodies, and healthcare professionals worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-start space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-medical-600 rounded-lg flex-shrink-0">
                    <achievement.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">
                        {achievement.title}
                      </h3>
                      <span className="text-sm font-medium text-medical-600 bg-medical-100 px-3 py-1 rounded-full">
                        {achievement.year}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3 leading-relaxed">
                      {achievement.description}
                    </p>
                    <span className="text-sm text-gray-500 font-medium">
                      {achievement.category}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Upcoming Events Section */}
      <section id="upcoming" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Upcoming Events
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join us at these upcoming industry events where we'll be showcasing our latest 
              innovations and sharing insights about the future of medical technology.
            </p>
          </div>

          <div className="space-y-8">
            {upcomingEvents.map((event, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="p-8">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                          {event.type}
                        </span>
                        <span className="bg-medical-100 text-medical-800 px-3 py-1 rounded-full text-sm font-medium">
                          Upcoming
                        </span>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        {event.title}
                      </h3>
                      <div className="flex flex-wrap items-center gap-4 text-gray-600 mb-4">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-2" />
                          {event.date}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-2" />
                          {event.location}
                        </div>
                      </div>
                      <p className="text-gray-700 leading-relaxed">
                        {event.description}
                      </p>
                    </div>
                    <div className="mt-6 lg:mt-0 lg:ml-8">
                      <button className="inline-flex items-center justify-center px-6 py-3 bg-medical-600 text-white font-semibold rounded-lg hover:bg-medical-700 transition-colors duration-200">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Learn More
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Past Events Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Past Events & Participation
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Look back at our successful participation in industry events, conferences, 
              and exhibitions where we've shared knowledge and built valuable partnerships.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {pastEvents.map((event, index) => (
              <div key={index} className="bg-gray-50 rounded-xl overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {event.type}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {event.title}
                  </h3>
                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {event.date}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2" />
                      {event.location}
                    </div>
                  </div>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    {event.description}
                  </p>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Highlights:</h4>
                    <ul className="space-y-1">
                      {event.highlights.map((highlight, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                          <div className="w-1.5 h-1.5 bg-medical-600 rounded-full mr-2"></div>
                          {highlight}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-medical-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Partner With Us at Future Events
          </h2>
          <p className="text-xl text-medical-100 mb-8 max-w-3xl mx-auto">
            Interested in collaborating with us at upcoming conferences, exhibitions, or industry events? 
            Let's explore partnership opportunities together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-medical-700 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Contact Partnership Team
            </a>
            <a
              href="/about"
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-medical-700 transition-colors duration-200"
            >
              Learn More About Us
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Events
