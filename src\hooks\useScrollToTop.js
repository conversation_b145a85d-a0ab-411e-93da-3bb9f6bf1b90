import { useCallback } from 'react'

export const useScrollToTop = () => {
  const scrollToTop = useCallback(() => {
    // Multiple approaches to ensure cross-browser compatibility
    try {
      // Method 1: Modern browsers
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'instant'
      })
    } catch (error) {
      // Method 2: Fallback for older browsers
      try {
        window.scrollTo(0, 0)
      } catch (error2) {
        // Method 3: Direct DOM manipulation
        document.documentElement.scrollTop = 0
        document.body.scrollTop = 0
      }
    }
  }, [])

  return scrollToTop
}
