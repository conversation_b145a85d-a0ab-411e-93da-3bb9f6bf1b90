import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { ArrowLeft, Heart, Shield, Award, Download, Phone, Mail, CheckCircle, Star } from 'lucide-react'
import { products } from '../data/products'
import SEOHead from '../components/SEOHead'
import { getProductSEO } from '../data/seoData'

const ProductDetail = () => {
  const { id } = useParams()
  const [activeTab, setActiveTab] = useState('overview')
  
  const product = products.find(p => p.id === parseInt(id))
  const productSEO = getProductSEO(product)

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <Link to="/products" className="text-medical-600 hover:text-medical-700">
            ← Back to Products
          </Link>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'features', label: 'Features' },
    { id: 'specifications', label: 'Specifications' },
    { id: 'certifications', label: 'Certifications' }
  ]

  const relatedProducts = products.filter(p => 
    p.category === product.category && p.id !== product.id
  ).slice(0, 3)

  return (
    <div className="min-h-screen bg-gray-50">
      {productSEO && <SEOHead {...productSEO} />}
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Link to="/" className="hover:text-medical-600">Home</Link>
            <span>/</span>
            <Link to="/products" className="hover:text-medical-600">Products</Link>
            <span>/</span>
            <span className="text-gray-900">{product.name}</span>
          </div>
        </div>
      </div>

      {/* Product Header */}
      <section className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Product Image */}
            <div className="space-y-4">
              <div className="aspect-w-4 aspect-h-3 bg-gray-100 rounded-xl overflow-hidden">
                <img
                  src={product.image}
                  alt={`${product.name} - ${product.description} - Medical Equipment by Foremost Meditech`}
                  className="w-full h-96 object-cover"
                />
              </div>
              <div className="flex space-x-2">
                <img src={product.image} alt={`${product.name} main view`} className="w-20 h-20 object-cover rounded-lg border-2 border-medical-600" />
                <img src={product.image} alt={`${product.name} side view`} className="w-20 h-20 object-cover rounded-lg border border-gray-200" />
                <img src={product.image} alt={`${product.name} detail view`} className="w-20 h-20 object-cover rounded-lg border border-gray-200" />
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <span className="bg-medical-100 text-medical-800 px-3 py-1 rounded-full text-sm font-medium">
                    {product.category}
                  </span>
                  <div className="flex space-x-1">
                    {product.certifications.includes('FDA 510(k)') && (
                      <div className="bg-green-100 text-green-800 p-1 rounded-full">
                        <Shield className="w-4 h-4" />
                      </div>
                    )}
                    {product.certifications.includes('ISO 13485') && (
                      <div className="bg-blue-100 text-blue-800 p-1 rounded-full">
                        <Award className="w-4 h-4" />
                      </div>
                    )}
                  </div>
                </div>
                <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  {product.name}
                </h1>
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                  <span className="text-sm text-gray-600 ml-2">(4.9/5 from 127 reviews)</span>
                </div>
                <p className="text-lg text-gray-600 leading-relaxed">
                  {product.description}
                </p>
              </div>

              {/* Quick Specs */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Specifications</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Tidal Volume:</span>
                    <span className="font-medium ml-2">{product.specifications['Tidal Volume']}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Respiratory Rate:</span>
                    <span className="font-medium ml-2">{product.specifications['Respiratory Rate']}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">PEEP Range:</span>
                    <span className="font-medium ml-2">{product.specifications['PEEP Range']}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Weight:</span>
                    <span className="font-medium ml-2">{product.specifications['Weight']}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link
                    to="/contact"
                    className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-medical-600 text-white font-semibold rounded-lg hover:bg-medical-700 transition-colors duration-200"
                  >
                    <Mail className="w-5 h-5 mr-2" />
                    Request Quote
                  </Link>
                  <a
                    href="tel:08045813521"
                    className="flex-1 inline-flex items-center justify-center px-6 py-3 border-2 border-medical-600 text-medical-600 font-semibold rounded-lg hover:bg-medical-600 hover:text-white transition-colors duration-200"
                  >
                    <Phone className="w-5 h-5 mr-2" />
                    Call Sales
                  </a>
                </div>
                <button className="w-full inline-flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 font-semibold rounded-lg hover:bg-gray-200 transition-colors duration-200">
                  <Download className="w-5 h-5 mr-2" />
                  Download Brochure (PDF)
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Details Tabs */}
      <section className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-medical-600 text-medical-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="py-12">
            {activeTab === 'overview' && (
              <div className="prose max-w-none">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Product Overview</h3>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  {product.description}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-4">Key Benefits</h4>
                    <ul className="space-y-3">
                      {product.features.slice(0, 4).map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-4">Applications</h4>
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <Heart className="w-5 h-5 text-medical-600 mt-0.5 mr-3 flex-shrink-0" />
                        Intensive Care Units
                      </li>
                      <li className="flex items-start">
                        <Heart className="w-5 h-5 text-medical-600 mt-0.5 mr-3 flex-shrink-0" />
                        Emergency Departments
                      </li>
                      <li className="flex items-start">
                        <Heart className="w-5 h-5 text-medical-600 mt-0.5 mr-3 flex-shrink-0" />
                        Operating Rooms
                      </li>
                      <li className="flex items-start">
                        <Heart className="w-5 h-5 text-medical-600 mt-0.5 mr-3 flex-shrink-0" />
                        Patient Transport
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'features' && (
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Features & Capabilities</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Technical Specifications</h3>
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="divide-y divide-gray-200">
                      {Object.entries(product.specifications).map(([key, value], index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {key}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                            {value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'certifications' && (
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Certifications & Compliance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {product.certifications.map((cert, index) => (
                    <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-center w-16 h-16 bg-medical-600 rounded-full mx-auto mb-4">
                        <Award className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">{cert}</h4>
                      <p className="text-sm text-gray-600">Certified and compliant</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <section className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Related Products</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedProducts.map(relatedProduct => (
                <Link
                  key={relatedProduct.id}
                  to={`/products/${relatedProduct.id}`}
                  className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group"
                >
                  <img
                    src={relatedProduct.image}
                    alt={`${relatedProduct.name} - ${relatedProduct.shortDescription} - Related Medical Equipment`}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-medical-600 transition-colors duration-200">
                      {relatedProduct.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      {relatedProduct.shortDescription}
                    </p>
                    <div className="text-xl font-bold text-medical-600">
                      {relatedProduct.price}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  )
}

export default ProductDetail
