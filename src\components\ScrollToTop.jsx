import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { useScrollToTop } from '../hooks/useScrollToTop'

const ScrollToTop = () => {
  const { pathname } = useLocation()
  const scrollToTop = useScrollToTop()

  useEffect(() => {
    // Immediate scroll
    scrollToTop()

    // Also try after a small delay to handle any async rendering
    const timeoutId = setTimeout(() => {
      scrollToTop()
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [pathname, scrollToTop])

  return null // This component doesn't render anything
}

export default ScrollToTop
