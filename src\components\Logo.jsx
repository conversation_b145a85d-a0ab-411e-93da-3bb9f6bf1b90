import React from 'react'

const Logo = ({ className = "", size = "default" }) => {
  const sizeClasses = {
    small: "h-8",
    default: "h-12",
    large: "h-16"
  }

  const isWhiteText = className.includes('text-white')

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo Icon */}
      <div className={`${sizeClasses[size]} aspect-square flex items-center justify-center`}>
        <svg 
          viewBox="0 0 100 100" 
          className="w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Background squares pattern similar to your logo */}
          <rect x="0" y="0" width="45" height="45" fill="#FF5722" rx="4"/>
          <rect x="55" y="0" width="45" height="20" fill="#666666" rx="2"/>
          <rect x="0" y="55" width="20" height="45" fill="#666666" rx="2"/>
          <rect x="30" y="55" width="20" height="45" fill="#666666" rx="2"/>
          
          {/* Medical cross icon overlay */}
          <g transform="translate(22.5, 22.5)">
            <circle cx="0" cy="0" r="12" fill="white" opacity="0.95"/>
            <path
              d="M-6 -2 L6 -2 L6 2 L-6 2 Z M-2 -6 L2 -6 L2 6 L-2 6 Z"
              fill="#FF5722"
            />
          </g>
        </svg>
      </div>

      {/* Company Name */}
      <div className="flex flex-col">
        <div className="flex items-baseline space-x-1">
          <span className={`font-bold ${isWhiteText ? 'text-white' : 'text-orange-600'} ${
            size === 'small' ? 'text-lg' :
            size === 'large' ? 'text-3xl' : 'text-2xl'
          }`}>
            foremost
          </span>
        </div>
        <div className="flex items-baseline space-x-1 -mt-1">
          <span className={`font-bold ${isWhiteText ? 'text-gray-300' : 'text-gray-700'} ${
            size === 'small' ? 'text-sm' :
            size === 'large' ? 'text-xl' : 'text-lg'
          }`}>
            meditech
          </span>
        </div>
        <p className={`font-medium leading-tight ${isWhiteText ? 'text-gray-400' : 'text-orange-500'} ${
          size === 'small' ? 'text-xs' :
          size === 'large' ? 'text-sm' : 'text-xs'
        }`}>
          Advanced Ventilation Solutions
        </p>
      </div>
    </div>
  )
}

export default Logo
