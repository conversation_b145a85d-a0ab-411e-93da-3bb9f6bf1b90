import { Link } from 'react-router-dom'
import { Phone, Mail, MapPin, Shield, Award, Globe } from 'lucide-react'
import Logo from './Logo'

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Logo size="default" className="text-white" />
            <p className="text-gray-300 text-sm leading-relaxed">
              Leading manufacturer of advanced ventilation systems, committed to saving lives through innovative medical technology and exceptional quality.
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center space-x-1 text-sm">
                <Shield className="w-4 h-4 text-green-400" />
                <span className="text-gray-300">CE Certified</span>
              </div>
              <div className="flex items-center space-x-1 text-sm">
                <Award className="w-4 h-4 text-blue-400" />
                <span className="text-gray-300">ISO Certified</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/products" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Products
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Contact
                </Link>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Support
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Documentation
                </a>
              </li>
            </ul>
          </div>

          {/* Product Categories */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Product Categories</h4>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  FMT 700 PLUS
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  FMT 2100 Ventilator
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Transport Ventilator FMT 700
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Portable Ventilator Machine
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-medical-400 transition-colors duration-200">
                  Anesthesia Workstations
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Information</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-medical-400 mt-0.5 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>NEAR TEHSIL OFFICE, SIRSA ROAD</p>
                  <p>NATHUSARI CHOPTA</p>
                  <p>Sirsa - 125110, Haryana, India</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-medical-400 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>***********</p>
                  <p className="text-xs text-gray-400">Business Hours Support</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-medical-400 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p><EMAIL></p>
                  <p className="text-xs text-gray-400">Response within 24 hours</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Globe className="w-5 h-5 text-medical-400 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>Pan India Service Network</p>
                  <p className="text-xs text-gray-400">25+ States Coverage</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-400">
              <p>&copy; 2024 Foremost Meditech. All rights reserved.</p>
            </div>
            <div className="flex space-x-6 text-sm text-gray-400">
              <a href="#" className="hover:text-medical-400 transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="hover:text-medical-400 transition-colors duration-200">
                Terms of Service
              </a>
              <a href="#" className="hover:text-medical-400 transition-colors duration-200">
                Quality Assurance
              </a>
              <a href="#" className="hover:text-medical-400 transition-colors duration-200">
                Regulatory Compliance
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
