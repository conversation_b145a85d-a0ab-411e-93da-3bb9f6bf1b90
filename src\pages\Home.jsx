import { Link } from 'react-router-dom'
import { ArrowRight, Heart, Shield, Award, Users, Globe, Clock, CheckCircle } from 'lucide-react'
import SEOHead from '../components/SEOHead'
import { seoData } from '../data/seoData'

const Home = () => {
  const features = [
    {
      icon: Heart,
      title: "Life-Saving Technology",
      description: "Advanced ventilation systems designed to provide critical life support when it matters most."
    },
    {
      icon: Shield,
      title: "FDA Approved & Certified",
      description: "All our products meet the highest regulatory standards and safety requirements."
    },
    {
      icon: Award,
      title: "Industry Leading Quality",
      description: "ISO 13485 certified manufacturing with rigorous quality control processes."
    },
    {
      icon: Users,
      title: "Expert Support",
      description: "24/7 technical support from our team of biomedical engineers and specialists."
    },
    {
      icon: Globe,
      title: "National Reach",
      description: "Serving healthcare facilities in over 25+ states."
    },
    {
      icon: Clock,
      title: "Rapid Response",
      description: "Emergency deployment and support services available around the clock."
    }
  ]

  const stats = [
    { number: "10,000+", label: "Lives Saved" },
    { number: "200+", label: "Hospitals Served" },
    { number: "25+", label: "States Covered" },
    { number: "10+", label: "Years Experience" }
  ]

  const productCategories = [
    {
      name: "Ventilator Machines",
      description: "Advanced ventilation systems for critical care and transport",
      image: "/api/placeholder/300/200",
      link: "/products"
    },
    {
      name: "Anesthesia Workstations",
      description: "Complete anesthesia delivery and monitoring systems",
      image: "/api/placeholder/300/200",
      link: "/products"
    }
  ]

  return (
    <div className="min-h-screen">
      <SEOHead {...seoData.home} />
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-medical-600 via-medical-700 to-medical-800 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Advanced Ventilation
                  <span className="block text-medical-200">Solutions</span>
                </h1>
                <p className="text-xl lg:text-2xl text-medical-100 leading-relaxed">
                  Saving lives through innovative medical technology and exceptional quality ventilation systems.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/products"
                  className="inline-flex items-center justify-center px-8 py-4 bg-white text-medical-700 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 group"
                >
                  Explore Products
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
                </Link>
                <Link
                  to="/contact"
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-medical-700 transition-colors duration-200"
                >
                  Contact Sales
                </Link>
              </div>

              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>FDA Approved</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>ISO Certified</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>24/7 Support</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                <img
                  src="/api/placeholder/500/400"
                  alt="Foremost Meditech FMT 700 PLUS Ventilator Machine - Advanced Medical Equipment for Critical Care"
                  className="w-full h-auto rounded-lg shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl lg:text-4xl font-bold text-medical-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Foremost Meditech?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We combine cutting-edge technology with unwavering commitment to quality, 
              ensuring healthcare professionals have the tools they need to save lives.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-medical-600 rounded-lg mb-6">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Product Categories Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Product Range
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive ventilation solutions for every healthcare setting, 
              from intensive care units to emergency response.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {productCategories.map((category, index) => (
              <Link
                key={index}
                to={category.link}
                className="group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="aspect-w-16 aspect-h-9">
                  <img
                    src={category.image}
                    alt={`${category.name} - ${category.description} by Foremost Meditech`}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-medical-600 transition-colors duration-200">
                    {category.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {category.description}
                  </p>
                  <div className="flex items-center text-medical-600 font-medium group-hover:translate-x-1 transition-transform duration-200">
                    Learn More
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-medical-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Ready to Save More Lives?
          </h2>
          <p className="text-xl text-medical-100 mb-8 max-w-3xl mx-auto">
            Contact our team today to learn how Foremost Meditech ventilation systems 
            can enhance your healthcare facility's capabilities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-medical-700 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              Get Quote
            </Link>
            <Link
              to="/products"
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-medical-700 transition-colors duration-200"
            >
              View All Products
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
