# How to Add Product Images - Foremost Meditech Website

## ✅ FIXED: Images Now Working!

## Quick Guide for Adding FMT 700 PLUS Image

### Step 1: Prepare Your Image
1. **Format**: Save your image as JPG or PNG
2. **Size**: Recommended 800x600 pixels or higher
3. **Quality**: High quality, professional product photo
4. **File Size**: Keep under 500KB for fast loading

### Step 2: Replace the Placeholder
1. Navigate to: `foremost-meditech/public/images/products/`
2. Replace the file `fmt-700-plus.svg` with your actual product image
3. **Important**: Rename your image to `fmt-700-plus.jpg` or `fmt-700-plus.png`

### Step 3: Update the Code (if needed)
1. If you use a different file extension, update `src/data/products.js`
2. Change line 6 from `"/images/products/fmt-700-plus.svg"` to `"/images/products/fmt-700-plus.jpg"`

### Step 4: Test the Image
1. Save the file
2. The website will automatically update (hot reload)
3. Check the Products page and Product Detail page for FMT 700 PLUS

## File Locations

### Product Images Directory:
```
foremost-meditech/
└── public/
    └── images/
        └── products/
            ├── fmt-700-plus.svg ← Replace this file
            ├── fmt-2100-ventilator.svg
            ├── transport-ventilator-fmt-700.svg
            └── (other product placeholders)
```

## Adding More Product Images

### For other products, follow the same pattern:

1. **FMT 2100 Ventilator**: Replace `fmt-2100-ventilator.jpg`
2. **Transport Ventilator FMT 700**: Replace `transport-ventilator-fmt-700.jpg`
3. **Portable Ventilator Machine**: Replace `portable-ventilator-machine.jpg`
4. **Medical Ventilator**: Replace `medical-ventilator.jpg`
5. **Anesthesia Workstation Ventilator**: Replace `anesthesia-workstation-ventilator.jpg`
6. **Anesthesia Workstation**: Replace `anesthesia-workstation.jpg`

## Image Specifications

### Recommended Settings:
- **Resolution**: 1200x900 pixels (4:3 aspect ratio)
- **Format**: JPG (for photos) or PNG (for graphics)
- **Quality**: 80-90% (balance between quality and file size)
- **File Size**: 200-500KB per image
- **Background**: Clean, professional background
- **Lighting**: Well-lit, clear product visibility

### Image Optimization Tools:
- **Online**: TinyPNG, Squoosh.app
- **Software**: Photoshop, GIMP, Canva
- **Mobile**: Snapseed, VSCO

## Troubleshooting

### If the image doesn't appear:
1. **Check filename**: Must match exactly (case-sensitive)
2. **Check file format**: Use .jpg or .png
3. **Clear browser cache**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
4. **Check file size**: Large files may not load properly

### If the image looks distorted:
1. **Check aspect ratio**: Use 4:3 ratio (e.g., 800x600, 1200x900)
2. **Resize properly**: Don't stretch or compress the image
3. **Use image editing software** to crop to correct proportions

## Current Status

✅ **Image structure set up** - Ready for your images
✅ **FMT 700 PLUS configured** - Just replace the placeholder file
⏳ **Other products** - Placeholders ready for your images

## Need Help?

If you encounter any issues:
1. Check the browser console for errors (F12 → Console)
2. Verify the file path and filename
3. Ensure the image file is not corrupted
4. Try a different image format (JPG vs PNG)

## Example File Structure After Adding Images:

```
products/
├── fmt-700-plus.jpg (YOUR IMAGE HERE)
├── fmt-2100-ventilator.jpg (placeholder)
├── transport-ventilator-fmt-700.jpg (placeholder)
├── portable-ventilator-machine.jpg (placeholder)
├── medical-ventilator.jpg (placeholder)
├── anesthesia-workstation-ventilator.jpg (placeholder)
├── anesthesia-workstation.jpg (placeholder)
├── index.js (configuration file - don't modify)
└── README.md (detailed instructions)
```

**That's it!** Your FMT 700 PLUS image will now appear throughout the website automatically.
